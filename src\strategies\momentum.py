# src/strategies/momentum.py
import numpy as np

# Optional numba import with fallback
try:
    from numba import njit, prange
except ImportError:
    # Create mock numba decorators for fallback
    def njit(func):
        return func  # Just return the function without JIT compilation
    def prange(n):
        return range(n)  # Use regular range instead of parallel range

# Optional pandas import with fallback
try:
    import pandas as pd
except ImportError:
    # Create mock pandas for fallback
    class MockPandas:
        def DataFrame(self, data=None):
            return data or {}
    pd = MockPandas()
import logging
import asyncio
import time
from typing import Dict, List, Optional, Tuple
from decimal import Decimal

# Optional sortedcontainers import with fallback
try:
    from sortedcontainers import SortedDict
except ImportError:
    # Create mock SortedDict for fallback
    class SortedDict(dict):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)

        def peekitem(self, index=-1):
            items = list(self.items())
            if items:
                return items[index]
            raise KeyError("dictionary is empty")

from redis import Redis

# Local imports
from .base import BaseStrategy, TradeSignal
from ..core.execution import OrderType, VWAPExecutor
from ..risk.exposure import CVaRCalculator
from ..utils.microstructure import OrderBookAnalyzer

logger = logging.getLogger("hft.momentum")

class MomentumStrategy(BaseStrategy):
    """Numba-optimized Momentum Strategy with Market Microstructure Analysis"""
    
    def __init__(self, config: Dict):
        super().__init__(config)
        self.config = config
        self.redis = Redis(host=config.get('redis_host', 'localhost'), 
                          port=config.get('redis_port', 6379))
        
        # Initialize core components
        self.executor = VWAPExecutor(config['execution'])
        self.risk_model = CVaRCalculator(config['risk'])
        self.obook_analyzer = OrderBookAnalyzer(config['order_book_depth'])

        # Strategy parameters from config
        self.min_spread = float(config.get('min_spread', 0.0001))
        self.max_position = float(config.get('max_position', 1000.0))
        self.base_size = float(config.get('base_size', 100.0))
        self.alpha = float(config.get('alpha', 0.01))
        self.risk_multiplier = float(config.get('risk_multiplier', 2.5))

        # State variables
        self.position = 0.0
        self.trade_counts = np.zeros(10000, dtype=np.int64)
        self.mid_prices = np.zeros(10000, dtype=np.float64)
        self.spreads = np.zeros(10000, dtype=np.float64)

    @staticmethod
    @njit(nogil=True, parallel=True)
    def _process_market_data(data: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Numba-optimized market data processor"""
        n = data.shape[0]
        prices = np.empty(n, dtype=np.float64)
        volumes = np.empty(n, dtype=np.float64)
        
        for i in prange(n):
            prices[i] = (data[i, 0] + data[i, 1]) / 2.0  # Mid price
            volumes[i] = data[i, 2]  # Volume
            
        return prices, volumes

    @staticmethod
    @njit(nogil=True)
    def _is_hft_signal(price_change: float, 
                      volume_change: float, 
                      spread: float, 
                      imbalance: float,
                      min_spread: float) -> bool:
        """Numba-optimized signal condition check"""
        return (
            abs(price_change) > min_spread * 2 and
            volume_change > 1.5 and
            spread < min_spread * 3 and
            imbalance > 0.7
        )

    async def analyze_market(self, market_data: Dict) -> List[TradeSignal]:
        """Main analysis pipeline"""
        try:
            start_time = time.perf_counter()
            symbols = list(market_data.keys())
            data_arrays = [np.array(market_data[sym]) for sym in symbols]
            
            # Process all symbols in parallel
            processed_data = await asyncio.gather(*[
                self._process_symbol(symbol, data)
                for symbol, data in zip(symbols, data_arrays)
            ])
            
            # Aggregate signals
            signals = []
            for res in processed_data:
                if res is not None:
                    signals.extend(res)
            
            # Apply risk management
            filtered_signals = await self._apply_risk_constraints(signals)
            
            logger.debug(f"Processing time: {time.perf_counter() - start_time:.6f}s")
            return filtered_signals
            
        except Exception as e:
            logger.error(f"Market analysis failed: {str(e)}")
            return []

    async def _process_symbol(self, symbol: str, data: np.ndarray) -> List[TradeSignal]:
        """Process data for a single symbol"""
        try:
            prices, volumes = self._process_market_data(data)
            spreads = await self.obook_analyzer.get_spreads(symbol)
            imbalance = await self.obook_analyzer.get_imbalance(symbol)
            
            signals = []
            for i in range(1, len(prices)):
                price_change = prices[i] - prices[i-1]
                volume_change = volumes[i] / max(volumes[i-1], 1e-6)
                
                if self._is_hft_signal(
                    price_change, 
                    volume_change,
                    spreads[i],
                    imbalance,
                    self.min_spread
                ):
                    signals.append(self._create_signal(
                        symbol, 
                        prices[i], 
                        volumes[i], 
                        1 if price_change > 0 else -1
                    ))
            
            return signals
            
        except Exception as e:
            logger.error(f"Error processing {symbol}: {str(e)}")
            return []

    def _create_signal(self, 
                      symbol: str, 
                      price: float, 
                      volume: float, 
                      direction: int) -> TradeSignal:
        """Create trade signal with proper decimal formatting"""
        return TradeSignal(
            symbol=symbol,
            side='buy' if direction > 0 else 'sell',
            price=Decimal(price).quantize(Decimal('1e-4')),
            amount=Decimal(self.base_size * volume).quantize(Decimal('1e-2')),
            timestamp=time.time_ns(),
            order_type=OrderType.LIMIT,
            time_in_force='IOC'
        )

    async def _apply_risk_constraints(self, signals: List[TradeSignal]) -> List[TradeSignal]:
        """Apply risk management constraints"""
        current_risk = self.risk_model.current_risk()
        max_risk = self.alpha * self.risk_multiplier
        filtered = []
        
        for signal in signals:
            signal_risk = abs(float(signal.amount)) * 0.01  # Simplified risk model
            if (current_risk + signal_risk) <= max_risk:
                filtered.append(signal)
                current_risk += signal_risk
                
        return filtered

    async def execute_trades(self, signals: List[TradeSignal]):
        """Execute batch trades with slippage protection"""
        try:
            if not signals:
                return []
                
            # Execute in parallel with rate limiting
            semaphore = asyncio.Semaphore(self.config.get('max_concurrent_orders', 100))
            async with semaphore:
                tasks = [self._execute_single(signal) for signal in signals]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Process results
                successful = [r for r in results if not isinstance(r, Exception)]
                self._update_positions(successful)
                return successful
                
        except Exception as e:
            logger.critical(f"Trade execution failed: {str(e)}")
            await self._emergency_shutdown()
            return []

    # region Order Book Management
async def _update_order_book(self, symbol: str, data: dict):
    """Update and maintain real-time order book state"""
    try:
        # Store in Redis for cross-process access
        await self.redis.hset(
            f"orderbook:{symbol}",
            mapping={
                "bids": str(data['bids']),
                "asks": str(data['asks']),
                "timestamp": str(time.time_ns())
            }
        )
        # Update local cache
        self.order_book[symbol] = {
            'bids': SortedDict({p: q for p, q in data['bids']}),
            'asks': SortedDict({p: q for p, q in data['asks']})
        }
    except Exception as e:
        self.logger.error(f"Order book update failed: {str(e)}")

def _get_best_bid_ask(self, symbol: str) -> tuple:
    """Get current best bid/ask with error handling"""
    try:
        return (
            self.order_book[symbol]['bids'].peekitem(-1)[0],  # Highest bid
            self.order_book[symbol]['asks'].peekitem(0)[0]    # Lowest ask
        )
    except (KeyError, IndexError):
        return (None, None)

def _calculate_market_imbalance(self, symbol: str) -> float:
    """Calculate order book imbalance metric"""
    try:
        bids = self.order_book[symbol]['bids']
        asks = self.order_book[symbol]['asks']
        total_bid = sum(bids.values())
        total_ask = sum(asks.values())
        return (total_bid - total_ask) / (total_bid + total_ask)
    except KeyError:
        return 0.0
# endregion

# region Position Management
def _update_position(self, executed_trades: list):
    """Update positions with thread-safe atomic operations"""
    for trade in executed_trades:
        if trade['status'] == 'filled':
            size = float(trade['filled_qty'])
            self.position += size if trade['side'] == 'buy' else -size
            
    # Enforce position limits
    if abs(self.position) > self.max_position:
        self._trigger_position_alert()

async def _calculate_unrealized_pnl(self) -> float:
    """Calculate unrealized PnL for open positions"""
    try:
        current_price = await self._get_market_price()
        return self.position * (current_price - self.avg_entry_price)
    except Exception as e:
        self.logger.error(f"PnL calculation error: {str(e)}")
        return 0.0

def _check_position_limits(self) -> bool:
    """Check against configured position limits"""
    return abs(self.position) < self.max_position * 0.95
# endregion

# region Risk Management
async def _check_max_drawdown(self) -> bool:
    """Circuit breaker for maximum allowed drawdown"""
    equity = await self._get_account_equity()
    max_dd = self.config['risk']['max_drawdown']
    if equity < self.initial_equity * (1 - max_dd):
        await self._emergency_shutdown("Max drawdown breached")
        return False
    return True

def _check_leverage(self) -> bool:
    """Validate current leverage ratio"""
    leverage = abs(self.position) / self.account_equity
    return leverage < self.config['risk']['max_leverage']

async def _check_sector_exposure(self, signal: TradeSignal) -> bool:
    """Sector exposure risk check using external API"""
    try:
        sector = await self.data_provider.get_asset_sector(signal.symbol)
        current_exposure = self.sector_exposure.get(sector, 0.0)
        return (current_exposure + float(signal.amount)) < \
               self.config['risk']['max_sector_exposure']
    except Exception as e:
        self.logger.warning(f"Sector check failed: {str(e)}")
        return False

async def _check_liquidity(self, symbol: str, size: float) -> bool:
    """Liquidity availability check"""
    try:
        daily_volume = await self.data_provider.get_daily_volume(symbol)
        return size < daily_volume * self.config['risk']['max_volume_ratio']
    except Exception as e:
        self.logger.error(f"Liquidity check failed: {str(e)}")
        return False
# endregion

# region Performance Monitoring
def _record_latency(self, event_type: str, duration: float):
    """Track latency metrics with statistical aggregation"""
    self.latency_stats[event_type].append(duration)
    # Maintain rolling window of 1000 samples
    if len(self.latency_stats[event_type]) > 1000:
        self.latency_stats[event_type].pop(0)

async def _calculate_sharpe_ratio(self, lookback: int = 30) -> float:
    """Calculate rolling Sharpe ratio of strategy returns"""
    returns = np.array(self.performance_stats['returns'][-lookback:])
    if len(returns) < 2:
        return 0.0
    return np.mean(returns) / np.std(returns) * np.sqrt(252)

async def _generate_performance_report(self) -> dict:
    """Generate comprehensive performance report"""
    return {
        'timestamp': time.time_ns(),
        'returns': self.performance_stats['returns'],
        'latency': {
            'mean': np.mean(self.latency_stats['process']),
            'p99': np.percentile(self.latency_stats['process'], 99)
        },
        'positions': self.position_history,
        'risk_metrics': {
            'max_drawdown': await self._calculate_max_drawdown(),
            'sharpe_ratio': await self._calculate_sharpe_ratio()
        }
    }
# endregion

# region Error Handling & Retries
@async_retry(max_retries=3, delay=0.1, backoff=2)
async def _retry_order_execution(self, order: dict):
    """Smart order retry with exponential backoff"""
    try:
        return await self.executor.execute_order(order)
    except OrderRejectedError as e:
        self.logger.warning(f"Order rejected: {str(e)}")
        raise
    except ExchangeError as e:
        self.logger.error(f"Exchange error: {str(e)}")
        raise

def _circuit_breaker_check(self) -> bool:
    """Monitor system health for emergency shutdown"""
    return (
        self.consecutive_errors > 10 or
        self.position > self.max_position * 1.1 or
        time.time() - self.last_success > 60
    )
# endregion

# region Data Management
def _normalize_data(self, raw_data: dict) -> np.ndarray:
    """Normalize and validate market data format"""
    required_keys = ['bid', 'ask', 'volume', 'timestamp']
    return np.array([[
        d.get('bid', np.nan),
        d.get('ask', np.nan),
        d.get('volume', 0.0),
        d.get('timestamp', time.time_ns())
    ] for d in raw_data if all(k in d for k in required_keys)], dtype=np.float64)

async def _clean_market_data(self, raw_data: list) -> list:
    """Data cleaning pipeline"""
    return [d for d in raw_data if await self._validate_data_point(d)]

async def _validate_data_point(self, data_point: dict) -> bool:
    """Validate individual market data point"""
    return (
        data_point['bid'] > 0 and
        data_point['ask'] > data_point['bid'] and
        data_point['volume'] >= 0 and
        data_point['timestamp'] > time.time_ns() - 1e9  # Within 1 second
    )
# endregion

# region Signal Validation
def _validate_signal_against_market_conditions(self, signal: TradeSignal) -> bool:
    """Context-aware signal validation"""
    current_spread = self._get_current_spread(signal.symbol)
    volatility = self.volatility_model.current_volatility(signal.symbol)
    return (
        current_spread < self.min_spread * 2 and
        volatility < self.config['risk']['max_volatility'] and
        signal.amount > self.config['execution']['min_order_size']
    )

def _filter_duplicate_signals(self, signals: list) -> list:
    """Remove duplicate signals using LRU cache"""
    seen = set()
    filtered = []
    for s in signals:
        key = (s.symbol, round(float(s.price), 4), round(float(s.amount), 2))
        if key not in seen:
            seen.add(key)
            filtered.append(s)
    return filtered
# endregion

# region Emergency Procedures
async def _emergency_shutdown(self, reason: str = "Unknown"):
    """Orderly emergency shutdown procedure"""
    self.logger.critical(f"Initiating emergency shutdown: {reason}")
    
    try:
        # Cancel all open orders
        await self.executor.cancel_all_orders()
        
        # Liquidate positions
        if self.config['risk']['auto_liquidate']:
            await self._liquidate_positions()
            
        # Save state for recovery
        self._save_state_for_recovery()
        
        # Notify monitoring systems
        await self._send_alert(f"Emergency shutdown: {reason}")
        
    except Exception as e:
        self.logger.error(f"Shutdown error: {str(e)}")
    finally:
        self.active = False

async def _liquidate_positions(self):
    """Gradual position liquidation using TWAP"""
    if abs(self.position) < self.config['execution']['min_lot_size']:
        return
    
    liquidation_order = {
        'symbol': self.symbol,
        'side': 'sell' if self.position > 0 else 'buy',
        'quantity': abs(self.position),
        'type': OrderType.TWAP,
        'duration': self.config['risk']['liquidation_duration']
    }
    
    await self.executor.execute_order(liquidation_order)
    self.position = 0.0

def _save_state_for_recovery(self):
    """Save critical state for system recovery"""
    state = {
        'position': self.position,
        'last_signals': self.trade_history[-100:],
        'order_book': dict(self.order_book),
        'timestamp': time.time_ns()
    }
    with open('recovery_state.pkl', 'wb') as f:
        pickle.dump(state, f)
# endregion

# region Configuration & Security
def _validate_config(self, config: dict) -> bool:
    """Comprehensive config validation"""
    required_keys = {
        'execution': ['type', 'max_slippage'],
        'risk': ['max_drawdown', 'stop_loss'],
        'parameters': ['min_spread', 'max_position']
    }
    
    for section, keys in required_keys.items():
        if section not in config:
            raise ValueError(f"Missing config section: {section}")
        for k in keys:
            if k not in config[section]:
                raise ValueError(f"Missing config key: {section}.{k}")
                
    return True

def _load_credentials(self) -> dict:
    """Secure credential loading from environment"""
    return {
        'api_key': os.getenv('EXCHANGE_API_KEY'),
        'api_secret': encrypt.decrypt(os.getenv('ENCRYPTED_SECRET'))
    }
# endregion

# region Logging & Reporting
def _log_trade(self, trade: dict):
    """Structured trade logging"""
    self.logger.info(json.dumps({
        'timestamp': time.time_ns(),
        'symbol': trade['symbol'],
        'side': trade['side'],
        'price': float(trade['price']),
        'quantity': float(trade['quantity']),
        'status': trade['status'],
        'latency': trade.get('latency', 0)
    }))

async def _send_alert(self, message: str):
    """Multi-channel alerting system"""
    try:
        if self.config['monitoring']['email_alerts']:
            await self._send_email_alert(message)
        if self.config['monitoring']['slack_alerts']:
            await self._send_slack_alert(message)
    except Exception as e:
        self.logger.error(f"Alert failed: {str(e)}")
# endregion