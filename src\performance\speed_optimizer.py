#!/usr/bin/env python3
"""
CRITICAL SPEED OPTIMIZATION SYSTEM
Implements aggressive speed optimization with strict latency targets
"""

import asyncio
import time
import logging
import threading
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque
import functools
import weakref

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Performance metrics for operations"""
    operation_name: str
    target_latency_ms: float
    actual_latency_ms: float
    success: bool
    timestamp: datetime
    component: str
    error_message: Optional[str] = None

@dataclass
class SpeedThresholds:
    """Speed thresholds for different operations"""
    signal_generation: float = 500.0  # ms
    order_execution: float = 1000.0   # ms
    balance_validation: float = 100.0  # ms
    neural_inference: float = 200.0   # ms
    api_calls: float = 300.0          # ms
    strategy_evaluation: float = 150.0 # ms
    component_timeout: float = 2000.0  # ms - terminate if exceeded

class CircuitBreaker:
    """Circuit breaker for slow components"""
    
    def __init__(self, failure_threshold: int = 5, timeout: float = 30.0):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.timeout:
                self.state = 'HALF_OPEN'
            else:
                raise RuntimeError(f"Circuit breaker OPEN for {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
            return result
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
                logger.error(f"Circuit breaker OPENED for {func.__name__}")
            
            raise e

class SpeedOptimizer:
    """Aggressive speed optimization system"""
    
    def __init__(self):
        self.thresholds = SpeedThresholds()
        self.metrics_history = deque(maxlen=1000)
        self.component_performance = defaultdict(list)
        self.circuit_breakers = {}
        self.slow_components = set()
        self.connection_pools = {}
        self.cache = {}
        self.cache_ttl = {}
        self.performance_alerts = []
        
        # Start background monitoring
        self._monitoring_active = True
        self._monitor_thread = threading.Thread(target=self._background_monitor, daemon=True)
        self._monitor_thread.start()
        
    def time_operation(self, operation_name: str, component: str, target_latency: float):
        """Decorator to time operations and enforce speed limits"""
        def decorator(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                success = True
                error_msg = None
                
                try:
                    # Use timeout to enforce speed limits
                    result = await asyncio.wait_for(
                        func(*args, **kwargs),
                        timeout=target_latency / 1000.0  # Convert ms to seconds
                    )
                    return result
                    
                except asyncio.TimeoutError:
                    success = False
                    error_msg = f"Operation timed out after {target_latency}ms"
                    logger.warning(f"⚠️ [SPEED] {operation_name} timed out: {target_latency}ms")
                    
                    # Mark component as slow
                    self.slow_components.add(component)
                    
                    # Terminate if exceeds component timeout
                    if target_latency > self.thresholds.component_timeout:
                        logger.error(f"❌ [SPEED] Component {component} terminated for excessive latency")
                        raise RuntimeError(f"Component {component} terminated for excessive latency")
                    
                    raise
                    
                except Exception as e:
                    success = False
                    error_msg = str(e)
                    raise
                    
                finally:
                    # Record metrics
                    actual_latency = (time.time() - start_time) * 1000  # Convert to ms
                    
                    metric = PerformanceMetrics(
                        operation_name=operation_name,
                        target_latency_ms=target_latency,
                        actual_latency_ms=actual_latency,
                        success=success,
                        timestamp=datetime.now(),
                        component=component,
                        error_message=error_msg
                    )
                    
                    self.metrics_history.append(metric)
                    self.component_performance[component].append(actual_latency)
                    
                    # Keep only recent performance data
                    if len(self.component_performance[component]) > 100:
                        self.component_performance[component] = self.component_performance[component][-50:]
                    
                    # Alert on slow performance
                    if actual_latency > target_latency:
                        self._alert_slow_performance(operation_name, component, actual_latency, target_latency)
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                success = True
                error_msg = None
                
                try:
                    result = func(*args, **kwargs)
                    return result
                    
                except Exception as e:
                    success = False
                    error_msg = str(e)
                    raise
                    
                finally:
                    actual_latency = (time.time() - start_time) * 1000
                    
                    metric = PerformanceMetrics(
                        operation_name=operation_name,
                        target_latency_ms=target_latency,
                        actual_latency_ms=actual_latency,
                        success=success,
                        timestamp=datetime.now(),
                        component=component,
                        error_message=error_msg
                    )
                    
                    self.metrics_history.append(metric)
                    self.component_performance[component].append(actual_latency)
                    
                    if actual_latency > target_latency:
                        self._alert_slow_performance(operation_name, component, actual_latency, target_latency)
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def cache_with_ttl(self, ttl_seconds: float = 5.0):
        """Cache results with TTL for speed optimization"""
        def decorator(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                # Create cache key
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
                
                # Check cache
                if cache_key in self.cache:
                    cache_time = self.cache_ttl.get(cache_key, 0)
                    if time.time() - cache_time < ttl_seconds:
                        return self.cache[cache_key]
                
                # Execute and cache
                result = await func(*args, **kwargs)
                self.cache[cache_key] = result
                self.cache_ttl[cache_key] = time.time()
                
                return result
            
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                cache_key = f"{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
                
                if cache_key in self.cache:
                    cache_time = self.cache_ttl.get(cache_key, 0)
                    if time.time() - cache_time < ttl_seconds:
                        return self.cache[cache_key]
                
                result = func(*args, **kwargs)
                self.cache[cache_key] = result
                self.cache_ttl[cache_key] = time.time()
                
                return result
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def get_circuit_breaker(self, component: str) -> CircuitBreaker:
        """Get or create circuit breaker for component"""
        if component not in self.circuit_breakers:
            self.circuit_breakers[component] = CircuitBreaker()
        return self.circuit_breakers[component]
    
    def _alert_slow_performance(self, operation: str, component: str, actual: float, target: float):
        """Alert on slow performance"""
        alert = {
            'timestamp': datetime.now(),
            'operation': operation,
            'component': component,
            'actual_ms': actual,
            'target_ms': target,
            'slowdown_factor': actual / target
        }
        
        self.performance_alerts.append(alert)
        
        # Keep only recent alerts
        if len(self.performance_alerts) > 100:
            self.performance_alerts = self.performance_alerts[-50:]
        
        logger.warning(f"⚠️ [SPEED] {operation} slow: {actual:.1f}ms > {target:.1f}ms (component: {component})")
    
    def _background_monitor(self):
        """Background monitoring thread"""
        while self._monitoring_active:
            try:
                # Clean old cache entries
                current_time = time.time()
                expired_keys = [
                    key for key, cache_time in self.cache_ttl.items()
                    if current_time - cache_time > 300  # 5 minutes
                ]
                
                for key in expired_keys:
                    self.cache.pop(key, None)
                    self.cache_ttl.pop(key, None)
                
                # Check for consistently slow components
                for component, latencies in self.component_performance.items():
                    if len(latencies) >= 10:
                        avg_latency = sum(latencies[-10:]) / 10
                        if avg_latency > 1000:  # 1 second average
                            logger.error(f"❌ [SPEED] Component {component} consistently slow: {avg_latency:.1f}ms avg")
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in speed monitor: {e}")
                time.sleep(60)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        recent_metrics = [m for m in self.metrics_history if m.timestamp > datetime.now() - timedelta(minutes=10)]
        
        if not recent_metrics:
            return {'status': 'no_data'}
        
        # Calculate statistics
        total_operations = len(recent_metrics)
        successful_operations = len([m for m in recent_metrics if m.success])
        success_rate = (successful_operations / total_operations) * 100
        
        # Average latencies by operation
        operation_stats = defaultdict(list)
        for metric in recent_metrics:
            operation_stats[metric.operation_name].append(metric.actual_latency_ms)
        
        avg_latencies = {
            op: sum(latencies) / len(latencies)
            for op, latencies in operation_stats.items()
        }
        
        # Component health
        component_health = {}
        for component, latencies in self.component_performance.items():
            if latencies:
                recent_latencies = latencies[-20:]  # Last 20 operations
                avg_latency = sum(recent_latencies) / len(recent_latencies)
                health_score = max(0, 100 - (avg_latency / 10))  # 100 = 0ms, 0 = 1000ms+
                
                component_health[component] = {
                    'avg_latency_ms': avg_latency,
                    'health_score': health_score,
                    'status': 'healthy' if health_score > 70 else 'degraded' if health_score > 30 else 'critical'
                }
        
        return {
            'status': 'operational',
            'total_operations': total_operations,
            'success_rate': success_rate,
            'avg_latencies': avg_latencies,
            'component_health': component_health,
            'slow_components': list(self.slow_components),
            'recent_alerts': self.performance_alerts[-10:],
            'circuit_breaker_status': {
                comp: cb.state for comp, cb in self.circuit_breakers.items()
            }
        }
    
    def shutdown(self):
        """Shutdown speed optimizer"""
        self._monitoring_active = False
        if self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5)

# Global speed optimizer instance
speed_optimizer = SpeedOptimizer()

# Convenience decorators
def fast_signal_generation(func):
    """Decorator for signal generation with 500ms target"""
    return speed_optimizer.time_operation("signal_generation", "trading", speed_optimizer.thresholds.signal_generation)(func)

def fast_order_execution(func):
    """Decorator for order execution with 1000ms target"""
    return speed_optimizer.time_operation("order_execution", "trading", speed_optimizer.thresholds.order_execution)(func)

def fast_balance_validation(func):
    """Decorator for balance validation with 100ms target"""
    return speed_optimizer.time_operation("balance_validation", "exchange", speed_optimizer.thresholds.balance_validation)(func)

def fast_neural_inference(func):
    """Decorator for neural inference with 200ms target"""
    return speed_optimizer.time_operation("neural_inference", "neural", speed_optimizer.thresholds.neural_inference)(func)

def fast_api_call(func):
    """Decorator for API calls with 300ms target"""
    return speed_optimizer.time_operation("api_call", "exchange", speed_optimizer.thresholds.api_calls)(func)

def fast_strategy_evaluation(func):
    """Decorator for strategy evaluation with 150ms target"""
    return speed_optimizer.time_operation("strategy_evaluation", "strategy", speed_optimizer.thresholds.strategy_evaluation)(func)

def cached_market_data(ttl_seconds: float = 5.0):
    """Cache market data with 5-second TTL"""
    return speed_optimizer.cache_with_ttl(ttl_seconds)
